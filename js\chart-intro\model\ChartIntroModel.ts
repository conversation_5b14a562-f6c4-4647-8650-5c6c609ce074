// Copyright 2022-2025, University of Colorado Boulder

/**
 * Model class for the 'Chart Intro' screen.
 *
 * <AUTHOR>
 */

import Property from '../../../../axon/js/Property.js';
import Vector2 from '../../../../dot/js/Vector2.js';
import Particle from '../../../../shred/js/model/Particle.js';
import ParticleAtom from '../../../../shred/js/model/ParticleAtom.js';
import Tandem from '../../../../tandem/js/Tandem.js';
import buildANucleus from '../../buildANucleus.js';
import BANConstants from '../../common/BANConstants.js';
import BANModel from '../../common/model/BANModel.js';
import BANParticle from '../../common/model/BANParticle.js';
import ParticleTypeEnum from '../../common/model/ParticleTypeEnum.js';
import DecayEquationModel from './DecayEquationModel.js';
import NuclideChartCellModel from './NuclideChartCellModel.js';
import ShellModelNucleus from './ShellModelNucleus.js';

// types
export type SelectedChartType = 'partial' | 'zoom';

class ChartIntroModel extends BANModel<ShellModelNucleus> {

  // The non-interactive mini-nucleus at the top of the screen.
  public readonly miniParticleAtom: ParticleAtom;

  // There's not an entry for all the neutron values, see POPULATED_CELLS.
  public static cellModelArray = BANModel.POPULATED_CELLS.map(
    ( neutronNumberList, protonNumber ) => neutronNumberList.map(
      neutronNumber => new NuclideChartCellModel( protonNumber, neutronNumber )
    )
  );

  public readonly decayEquationModel: DecayEquationModel;
  public readonly selectedNuclideChartProperty: Property<SelectedChartType>;

  public constructor() {

    // Empirically determined, the last nuclide the NuclideChartIntro screen goes up to is Neon-22 (10 protons
    // and 12 neutrons).
    super( BANConstants.CHART_MAX_NUMBER_OF_PROTONS, BANConstants.CHART_MAX_NUMBER_OF_NEUTRONS, new ShellModelNucleus() );

    // This is the mini-nucleus that updates based on the particleAtom.
    this.miniParticleAtom = new ParticleAtom( {
      tandem: Tandem.OPT_OUT // Opt out for now until phet-io is implemented.
    } );

    this.selectedNuclideChartProperty = new Property<SelectedChartType>( 'partial' );

    this.decayEquationModel = new DecayEquationModel( ChartIntroModel.cellModelArray,
      this.particleAtom.protonCountProperty, this.particleAtom.massNumberProperty );
  }

  /**
   * Create model for particle in mini-nucleus and add it to the miniParticleAtom.
   */
  public createMiniParticleModel( particleType: ParticleTypeEnum ): Particle {
    const particle = new BANParticle( particleType.particleTypeString );
    this.miniParticleAtom.addParticle( particle );
    return particle;
  }

  /**
   * Remove the particle from its shell position in the ShellModelNucleus.
   */
  public override removeParticle( particle: Particle ): void {
    this.particleAtom.removeParticleFromShell( particle );
    super.removeParticle( particle );
  }

  /**
   * Select the particle in the farthest energy level.
   */
  public override getParticleToReturn( particleType: ParticleTypeEnum, creatorNodePosition: Vector2 ): Particle {
    const particleToReturn = this.particleAtom.getLastParticleInShell( particleType );
    assert && assert( particleToReturn, 'No particle of type ' + particleType.name + ' exists in the particleAtom.' );
    assert && assert( !particleToReturn!.isDisposed, 'Particle should not already be disposed.' );

    // We know that sortedParticles is not empty, and does not contain null.
    return particleToReturn!;
  }

  /**
   * Return the next open shell position for the given particleType and add it to that shell position.
   */
  public override getParticleDestination( particleType: ParticleTypeEnum, particle: Particle ): Vector2 {
    return this.particleAtom.getParticleDestination( particleType, particle );
  }

  public override reset(): void {

    // This subset of particles needs manual clean-up since it's not part of the regular particles array.
    this.outgoingParticles.forEach( particle => {
      if ( !this.particles.includes( particle ) ) {
        particle.dispose();
      }
    } );
    super.reset();
    this.selectedNuclideChartProperty.reset();
    this.decayEquationModel.reset();

    // Put this last to make sure that this.particleAtom can be cleared first (by supertype).
    this.miniParticleAtom.clear();
  }

  /**
   * @param dt - time step, in seconds
   */
  public override step( dt: number ): void {
    super.step( dt );

    const stepParticle = ( particle: Particle ) => {
      assert && assert( !this.outgoingParticles.includes( particle ), 'should not double step particle' );
      assert && assert( !this.particles.includes( particle ), 'should not double step particle' );
      particle.step( dt );
    };

    // Step the miniParticleAtom nucleons.
    this.miniParticleAtom.protons.forEach( stepParticle );
    this.miniParticleAtom.neutrons.forEach( stepParticle );

    // When decaying, the animated particle from the miniParticleAtom is removed from it, but this particle still needs
    // to be stepped off of the screen.
    this.outgoingParticles.forEach( particle => {
      if ( !this.particles.includes( particle ) ) {
        particle.step( dt );
      }
    } );
  }

  /**
   * We need to make sure that the shell position spots reserved for the incoming, animating particles, are cleared out
   * since the particle is no longer coming into the atom.
   */
  public override clearIncomingParticle( particle: Particle, particleType: ParticleTypeEnum ): void {
    super.clearIncomingParticle( particle, particleType );

    // Not a full removeParticle() call because we never completed the animation into the particleAtom (but we did
    // count it in a shell position).
    this.particleAtom.removeParticleFromShell( particle );
  }
}

buildANucleus.register( 'ChartIntroModel', ChartIntroModel );
export default ChartIntroModel;