<!DOCTYPE HTML>
<!-- Top-level HTML file for build-a-nucleus generated by 'grunt generate-development-html' -->
<html>
<head>
  <meta charset="utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
  <meta name="viewport" content="initial-scale=1,user-scalable=no,maximum-scale=1"/>
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="phet-sim-level" content="development">

  <title>build-a-nucleus</title>
</head>

<!-- body is only made black for the loading phase so that the splash screen is black -->
<body style="background-color:black;">


<script type="text/javascript">
  /* eslint-disable quotes, quote-props */

  window.phet = window.phet || {};
  window.phet.chipper = window.phet.chipper || {};
  window.phet.chipper.packageObject =
    {
      "name": "build-a-nucleus",
      "version": "1.2.0-dev.1",
      "license": "GPL-3.0",
      "repository": {
        "type": "git",
        "url": "https://github.com/phetsims/build-a-nucleus.git"
      },
      "devDependencies": {
        "grunt": "~1.5.3"
      },
      "phet": {
        "requirejsNamespace": "BUILD_A_NUCLEUS",
        "phetLibs": [
          "bamboo",
          "shred",
          "twixt"
        ],
        "simulation": true,
        "runnable": true,
        "simFeatures": {
          "supportsInteractiveDescription": true,
          "supportsDynamicLocale": true,
          "supportsInteractiveHighlights": false
        },
        "supportedBrands": [
          "phet",
          "adapted-from-phet"
        ],
        "supportsOutputJS": true,
        "screenNameKeys": [
          "BUILD_A_NUCLEUS/screen.decay",
          "BUILD_A_NUCLEUS/screen.chartIntro"
        ],
        "published": true
      }
    };
  window.phet.chipper.stringRepos =
    [
      {
        "repo": "bamboo",
        "requirejsNamespace": "BAMBOO"
      },
      {
        "repo": "build-a-nucleus",
        "requirejsNamespace": "BUILD_A_NUCLEUS"
      },
      {
        "repo": "joist",
        "requirejsNamespace": "JOIST"
      },
      {
        "repo": "scenery-phet",
        "requirejsNamespace": "SCENERY_PHET"
      },
      {
        "repo": "shred",
        "requirejsNamespace": "SHRED"
      },
      {
        "repo": "sun",
        "requirejsNamespace": "SUN"
      },
      {
        "repo": "tambo",
        "requirejsNamespace": "TAMBO"
      },
      {
        "repo": "twixt",
        "requirejsNamespace": "TWIXT"
      }
    ];
  window.phet.chipper.allowLocaleSwitching = true;

  // Identify the brand (assume generated brand if not provided with query parameters)
  const brandMatch = location.search.match( /brand=([^&]+)/ );
  const brand = brandMatch ? decodeURIComponent( brandMatch[ 1 ] ) : 'adapted-from-phet';

  // Preloads, with more included for phet-io brand
  let preloads = [
    '../joist/js/splash.js',
    '../sherpa/lib/jquery-2.1.0.js',
    '../sherpa/lib/lodash-4.17.4.js',
    '../sherpa/lib/FileSaver-b8054a2.js',
    '../sherpa/lib/linebreak-1.1.0.js',
    '../sherpa/lib/flatqueue-1.2.1.js',
    '../sherpa/lib/paper-js-0.12.17.js',
    '../sherpa/lib/he-1.1.1.js',
    '../assert/js/assert.js',
    '../query-string-machine/js/QueryStringMachine.js',
    '../chipper/js/browser/initialize-globals.js',
    '../sherpa/lib/seedrandom-2.4.2.js',
    '../sherpa/lib/base64-js-1.2.0.js',
    '../sherpa/lib/TextEncoderLite-3c9f6f0.js'
  ];

  const query = new URLSearchParams( window.location.search );
  const esbuild = !!query.has( 'esbuild' );
  const liveReload = !!query.has( 'liveReload' );

  // RichText imports himalaya but it is skipped by esbuild, so we add it as a preload here.
  // see https://github.com/phetsims/scenery/issues/1583 and https://github.com/phetsims/chipper/issues/1409 and https://github.com/phetsims/membrane-transport/issues/6#issuecomment-2631403462
  if ( esbuild ) {
    preloads.push( '../sherpa/lib/himalaya-1.1.0.js' );
  }

  if ( brand === 'phet-io' ) {
    preloads = preloads.concat( [
      '../phet-io/js/phet-io-initialize-globals.js',
      '../build-a-nucleus/js/build-a-nucleus-phet-io-overrides.js'
    ] );
  }

  // Loads a synchronously-executed asynchronously-downloaded script tag, with optional data-main parameter.
  // See http://www.html5rocks.com/en/tutorials/speed/script-loading/ for more about script loading. It helps to
  // load all of the scripts with this method, so they are treated the same (and placed in the correct execution
  // order).
  const loadURL = ( preloadURL, type = 'text/javascript' ) => {
    const script = document.createElement( 'script' );
    script.type = type;
    script.src = preloadURL;
    script.async = false;
    script.setAttribute( 'crossorigin', 'use-credentials' );
    document.head.appendChild( script );
  };

  // Kick off string loading immediately
  loadURL( '../chipper/js/browser/load-unbuilt-strings.js' );

  // Queue all of the preloads to be loaded.
  preloads.forEach( preload => loadURL( preload ) );

  const targetSource = esbuild ? '../chipper/dist/dev-server-runnable/build-a-nucleus.js' : '../chipper/dist/js/build-a-nucleus/js/build-a-nucleus-main.js';

  // Module loading in compilation-free (development) mode will be kicked off once strings are loaded.
  // This is done in load-unbuilt-strings.js
  window.phet.chipper.loadModules = () => loadURL( targetSource, 'module' );

    // Live reload, see https://esbuild.github.io/api/#live-reload
  if ( liveReload ) {
    new EventSource( '/esbuild' ).addEventListener( 'change', () => location.reload() );
  }
</script>
</body>
</html>